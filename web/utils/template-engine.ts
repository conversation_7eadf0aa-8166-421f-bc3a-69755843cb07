import { ResumeTemplate } from './resume-templates/resumeTemplates';
import { StructuredResumeData } from '../types/resume-structured';
import { convertToTemplateData, validateTemplateData, ResumeTemplateData } from '../types/template-data';

// Import precompiled templates
import cleanProfessionalTemplate from './handlebars-templates/clean-professional.hbs';
import modernCleanTemplate from './handlebars-templates/modern-clean.hbs';
import classicProfessionalTemplate from './handlebars-templates/classic-professional.hbs';
import minimalistProfessionalTemplate from './handlebars-templates/minimalist-professional.hbs';
import executiveProfessionalTemplate from './handlebars-templates/executive-professional.hbs';
import corporateStandardTemplate from './handlebars-templates/corporate-standard.hbs';

// Import and register Handlebars helpers
import * as Handlebars from 'handlebars/runtime';
import * as helpers from './handlebars-helpers/index.js';

/**
 * Template compilation cache for performance
 * Key: template ID, Value: precompiled Handlebars template
 */
const precompiledTemplates = new Map<string, any>();

// Register all Handlebars helpers
Object.keys(helpers).forEach(helperName => {
  Handlebars.registerHelper(helperName, (helpers as any)[helperName]);
});

// Initialize precompiled templates
precompiledTemplates.set('clean-professional', cleanProfessionalTemplate);
precompiledTemplates.set('modern-clean', modernCleanTemplate);
precompiledTemplates.set('classic-professional', classicProfessionalTemplate);
precompiledTemplates.set('minimalist-professional', minimalistProfessionalTemplate);
precompiledTemplates.set('executive-professional', executiveProfessionalTemplate);
precompiledTemplates.set('corporate-standard', corporateStandardTemplate);

/**
 * Get precompiled template by ID
 * @param templateId - Template ID
 * @returns Precompiled Handlebars template or null if not found
 */
function getPrecompiledTemplate(templateId: string): any {
  const template = precompiledTemplates.get(templateId);
  if (!template) {
    throw new Error(`Precompiled template not found for ID: ${templateId}`);
  }
  return template;
}

/**
 * Validate template data and provide detailed error information
 * @param templateData - Template data to validate
 * @returns Validation result with details
 */
function validateTemplateDataWithDetails(templateData: ResumeTemplateData): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Validate required fields
  const validation = validateTemplateData(templateData);
  if (!validation.isValid) {
    errors.push(...validation.missingFields.map(field => `Missing required field: ${field}`));
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Main function to fill resume template with structured data
 * @param template - Resume template object
 * @param data - Structured resume data
 * @returns Filled HTML string
 * @throws Error if template compilation fails or data validation fails
 */
export function fillResumeTemplate(template: ResumeTemplate, data: StructuredResumeData): string {
  try {
    // Convert structured data to template format
    const templateData = convertToTemplateData(data);
    
    // Validate template data
    const validation = validateTemplateDataWithDetails(templateData);
    
    if (!validation.isValid) {
      throw new Error(`Template data validation failed: ${validation.errors.join(', ')}`);
    }
    
    // Log warnings if any
    if (validation.warnings.length > 0) {
      console.warn('Template data warnings:', validation.warnings);
    }
    
    // Get precompiled template
    const precompiledTemplate = getPrecompiledTemplate(template.id);
    
    // Generate HTML with additional error context
    let html: string;
    try {
      html = precompiledTemplate(templateData);
    } catch (renderError) {
      const renderErrorMessage = renderError instanceof Error ? renderError.message : 'Unknown render error';
      throw new Error(`Template rendering failed: ${renderErrorMessage}. This might be due to missing or malformed data in the template.`);
    }
    
    // Basic validation of generated HTML
    if (!html || html.trim().length === 0) {
      throw new Error('Generated HTML is empty');
    }
    
    if (templateData.name && !html.includes(templateData.name)) {
      throw new Error('Generated HTML does not contain the candidate name');
    }
    
    return html;
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    // Log error for debugging with more context
    console.error('Template fill error:', {
      templateId: template.id,
      templateName: template.name,
      error: errorMessage,
      candidateName: data?.personalInfo?.fullName || 'Unknown',
      dataStructure: {
        hasPersonalInfo: !!data?.personalInfo,
        hasExperiences: !!data?.experiences && Array.isArray(data.experiences),
        experiencesCount: data?.experiences?.length || 0,
        hasEducation: !!data?.education && Array.isArray(data.education),
        educationCount: data?.education?.length || 0,
        hasSkills: !!data?.skills?.categories && Array.isArray(data.skills.categories),
        skillsCount: data?.skills?.categories?.length || 0,
      }
    });
    
    throw new Error(`Failed to fill template "${template.name}": ${errorMessage}`);
  }
}

/**
 * Get available precompiled template IDs
 */
export function getAvailableTemplateIds(): string[] {
  return Array.from(precompiledTemplates.keys());
}

/**
 * Check if a template is available
 */
export function isTemplateAvailable(templateId: string): boolean {
  return precompiledTemplates.has(templateId);
}

/**
 * Multi-page content splitting utility
 * Splits resume content into multiple pages when it overflows A4 dimensions
 */
export interface MultiPageResult {
  pages: string[];
  totalPages: number;
  hasOverflow: boolean;
}

/**
 * Create multi-page resume HTML when content overflows
 * @param template - Resume template object
 * @param data - Structured resume data
 * @returns Multi-page HTML result
 */
export function createMultiPageResume(template: ResumeTemplate, data: StructuredResumeData): MultiPageResult {
  try {
    // First, generate the single-page HTML
    const singlePageHtml = fillResumeTemplate(template, data);

    // Check if content needs to be split into multiple pages
    const needsMultiPage = checkIfContentNeedsMultiPage(singlePageHtml);

    if (!needsMultiPage) {
      return {
        pages: [singlePageHtml],
        totalPages: 1,
        hasOverflow: false
      };
    }

    // Split content into multiple pages
    const pages = splitContentIntoPages(singlePageHtml, template);

    return {
      pages,
      totalPages: pages.length,
      hasOverflow: pages.length > 1
    };

  } catch (error) {
    console.error('Multi-page resume creation failed:', error);
    // Fallback to single page
    const fallbackHtml = fillResumeTemplate(template, data);
    return {
      pages: [fallbackHtml],
      totalPages: 1,
      hasOverflow: false
    };
  }
}

/**
 * Check if content needs to be split into multiple pages
 * This is a heuristic based on content length and structure
 */
function checkIfContentNeedsMultiPage(html: string): boolean {
  // Create a temporary DOM element to measure content
  if (typeof document === 'undefined') {
    // Server-side fallback - use content length heuristic
    return html.length > 15000; // Rough estimate
  }

  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;
  tempDiv.style.position = 'absolute';
  tempDiv.style.visibility = 'hidden';
  tempDiv.style.width = '210mm';
  tempDiv.style.fontSize = '10pt';
  tempDiv.style.lineHeight = '1.3';

  document.body.appendChild(tempDiv);

  const height = tempDiv.scrollHeight;
  document.body.removeChild(tempDiv);

  // A4 height is approximately 1123px at 96 DPI
  return height > 1123;
}

/**
 * Split content into multiple pages
 * This function intelligently breaks content at section boundaries
 */
function splitContentIntoPages(html: string, template: ResumeTemplate): string[] {
  // Parse the HTML to extract sections
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');
  const resumeContainer = doc.querySelector('.resume-container');

  if (!resumeContainer) {
    return [html]; // Fallback if structure is unexpected
  }

  // Extract header and sections
  const header = resumeContainer.querySelector('.header');
  const sections = Array.from(resumeContainer.querySelectorAll('.section'));

  if (!header || sections.length === 0) {
    return [html]; // Fallback if structure is unexpected
  }

  // Create pages by distributing sections
  const pages: string[] = [];
  let currentPageSections: Element[] = [];
  let currentPageHeight = 0;

  // Estimate header height (roughly 150px)
  const headerHeight = 150;
  currentPageHeight += headerHeight;

  for (const section of sections) {
    // Estimate section height based on content
    const sectionHeight = estimateSectionHeight(section);

    // If adding this section would exceed page height, start a new page
    if (currentPageHeight + sectionHeight > 1000 && currentPageSections.length > 0) {
      // Create page with current sections
      pages.push(createPageHtml(header, currentPageSections, template, pages.length + 1));

      // Start new page
      currentPageSections = [section];
      currentPageHeight = headerHeight + sectionHeight;
    } else {
      // Add section to current page
      currentPageSections.push(section);
      currentPageHeight += sectionHeight;
    }
  }

  // Add the last page if it has content
  if (currentPageSections.length > 0) {
    pages.push(createPageHtml(header, currentPageSections, template, pages.length + 1));
  }

  return pages.length > 0 ? pages : [html];
}

/**
 * Estimate the height of a section in pixels
 */
function estimateSectionHeight(section: Element): number {
  const content = section.innerHTML;

  // Base height for section title and margins
  let height = 40;

  // Estimate based on content type and length
  if (section.classList.contains('experience-item') || content.includes('experience-item')) {
    // Experience sections tend to be taller
    const experienceItems = section.querySelectorAll('.experience-item').length || 1;
    height += experienceItems * 80;
  } else if (section.classList.contains('education-item') || content.includes('education-item')) {
    // Education sections are medium height
    const educationItems = section.querySelectorAll('.education-item').length || 1;
    height += educationItems * 50;
  } else if (content.includes('skills-grid') || content.includes('skill-category')) {
    // Skills sections are usually compact
    height += 60;
  } else {
    // General content estimation based on text length
    const textLength = section.textContent?.length || 0;
    height += Math.max(30, textLength / 10);
  }

  return Math.min(height, 400); // Cap at reasonable maximum
}

/**
 * Get base page styles that are common across all templates
 */
function getBasePageStyle(): string {
  return `
    .resume-container * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    .resume-container {
      width: 210mm;
      height: 297mm;
      margin: 0 auto;
      background-color: white;
      page-break-inside: avoid;
      box-sizing: border-box;
      overflow: hidden;
      position: relative;
    }

    .page-number {
      position: absolute;
      bottom: 10mm;
      right: 15mm;
      font-size: 9pt;
      color: #666;
    }

    @media print {
      .resume-container {
        width: 210mm !important;
        height: 297mm !important;
        margin: 0 !important;
        page-break-inside: avoid !important;
        box-sizing: border-box !important;
      }
    }

    @page {
      size: A4;
      margin: 0;
    }
  `;
}

/**
 * Get clean professional template styles
 */
function getCleanProfessionalPageStyle(): string {
  return `
    ${getBasePageStyle()}

    .resume-container {
      font-family: 'Arial', sans-serif;
      line-height: 1.3;
      color: #333;
      padding: 10mm 15mm;
      font-size: 10pt;
    }

    .resume-container .header {
      text-align: center;
      margin-bottom: 16px;
      border-bottom: 2px solid #333;
      padding-bottom: 10px;
    }

    .resume-container .name {
      font-size: 20pt;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 4px;
    }

    .resume-container .title {
      font-size: 12pt;
      color: #7f8c8d;
      margin-bottom: 6px;
    }

    .resume-container .section-title {
      font-size: 11pt;
      font-weight: bold;
      color: #2c3e50;
      text-transform: uppercase;
      margin-bottom: 6px;
      border-bottom: 1px solid #bdc3c7;
      padding-bottom: 2px;
    }
  `;
}

/**
 * Get modern clean template styles
 */
function getModernCleanPageStyle(): string {
  return `
    ${getBasePageStyle()}

    .resume-container {
      font-family: 'Arial', sans-serif;
      font-size: 10pt;
      line-height: 1.4;
      color: #333;
      padding: 10mm 15mm;
    }

    .resume-container .header {
      text-align: center;
      margin-bottom: 20px;
    }

    .resume-container .name {
      font-size: 20pt;
      font-weight: bold;
      color: #2c3e50;
    }

    .resume-container .title {
      font-size: 12pt;
      color: #7f8c8d;
      margin-bottom: 6px;
    }
  `;
}

/**
 * Get corporate standard template styles
 */
function getCorporateStandardPageStyle(): string {
  return `
    ${getBasePageStyle()}

    .resume-container {
      font-family: 'Times New Roman', serif;
      font-size: 11pt;
      line-height: 1.3;
      color: #000000;
      padding: 15mm 20mm;
    }

    .resume-container .header {
      text-align: center;
      margin-bottom: 25px;
      padding-bottom: 15px;
      border-bottom: 2px solid #000000;
    }

    .resume-container .name {
      font-size: 18pt;
      font-weight: bold;
      color: #000000;
      text-transform: uppercase;
      letter-spacing: 1px;
      margin-bottom: 8px;
    }
  `;
}

/**
 * Get default template styles (fallback)
 */
function getDefaultPageStyle(): string {
  return getCleanProfessionalPageStyle();
}

/**
 * Create HTML for a single page
 */
function createPageHtml(header: Element, sections: Element[], template: ResumeTemplate, pageNumber: number): string {
  // Get template-specific styles
  let pageStyle = '';

  switch (template.id) {
    case 'clean-professional':
      pageStyle = getCleanProfessionalPageStyle();
      break;
    case 'modern-clean':
      pageStyle = getModernCleanPageStyle();
      break;
    case 'corporate-standard':
      pageStyle = getCorporateStandardPageStyle();
      break;
    default:
      pageStyle = getDefaultPageStyle();
  }

  // Create page HTML
  const pageHtml = `
    <style>
      ${pageStyle}
    </style>
    <div class="resume-container">
      ${header.outerHTML}
      ${sections.map(section => section.outerHTML).join('')}
      <div class="page-number">Page ${pageNumber}</div>
    </div>
  `;

  return pageHtml;
}

/**
 * Test template compilation with sample data
 * @param template - Template to test
 * @param sampleData - Sample structured resume data
 * @returns Test result
 */
export function testTemplate(template: ResumeTemplate, sampleData: StructuredResumeData): {
  success: boolean;
  error?: string;
  warnings?: string[];
  htmlLength?: number;
} {
  try {
    const html = fillResumeTemplate(template, sampleData);
    const templateData = convertToTemplateData(sampleData);
    const validation = validateTemplateDataWithDetails(templateData);

    return {
      success: true,
      warnings: validation.warnings,
      htmlLength: html.length
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}